<?php
/**
 * Q-<PERSON>usher PWA Settings
 *
 * @package Q-Pusher
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class Q_PWA_Settings
 *
 * Handles PWA settings for Q-Pusher
 */
class Q_PWA_Settings
{
    /**
     * Initialize the PWA settings
     */
    public static function init()
    {
        add_action('admin_menu', [self::class, 'add_settings_page']);
        add_action('admin_init', [self::class, 'register_settings']);
        add_action('admin_enqueue_scripts', [self::class, 'enqueue_admin_scripts']);
    }

    /**
     * Enqueue admin scripts and styles
     */
    public static function enqueue_admin_scripts($hook)
    {
        // Only load on our settings page
        if ($hook !== 'settings_page_q-pusher-pwa-settings') {
            return;
        }

        // Add inline styles for tabs with modern design
        wp_add_inline_style('wp-admin', '
            /* Modern PWA Settings Page */
            .q-pwa-settings-wrap {
                max-width: 1200px;
                margin: 20px 0;
            }

            .q-pwa-settings-wrap h1 {
                margin-bottom: 30px;
                font-weight: 600;
                color: #23282d;
            }

            /* Modern Tabs */
            .q-pwa-tabs {
                display: flex;
                flex-wrap: wrap;
                margin-bottom: 30px;
                border-bottom: 2px solid #f0f0f0;
                gap: 5px;
            }

            .q-pwa-tabs .q-pwa-tab {
                padding: 12px 20px;
                background: transparent;
                cursor: pointer;
                font-weight: 500;
                color: #555;
                border-radius: 4px 4px 0 0;
                transition: all 0.3s ease;
                border: none;
                position: relative;
                margin-bottom: -2px;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .q-pwa-tabs .q-pwa-tab .dashicons {
                font-size: 18px;
                width: 18px;
                height: 18px;
                transition: all 0.3s ease;
            }

            .q-pwa-tabs .q-pwa-tab:hover {
                color: #2271b1;
                background-color: rgba(34, 113, 177, 0.05);
            }

            .q-pwa-tabs .q-pwa-tab:hover .dashicons {
                color: #2271b1;
            }

            .q-pwa-tabs .q-pwa-tab.active {
                color: #2271b1;
                font-weight: 600;
                border-bottom: 2px solid #2271b1;
            }

            .q-pwa-tabs .q-pwa-tab.active .dashicons {
                color: #2271b1;
            }

            .q-pwa-tabs .q-pwa-tab.active:after {
                content: "";
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 100%;
                height: 2px;
                background: #2271b1;
            }

            /* Tab Content */
            .q-pwa-tab-content {
                display: none;
                background: #fff;
                border-radius: 4px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
                padding: 25px;
                margin-bottom: 20px;
                animation: fadeIn 0.3s ease-in-out;
            }

            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(5px); }
                to { opacity: 1; transform: translateY(0); }
            }

            .q-pwa-tab-content.active {
                display: block;
            }

            .q-pwa-tab-content h2 {
                margin-top: 0;
                font-size: 1.5em;
                font-weight: 600;
                color: #23282d;
                padding-bottom: 15px;
                border-bottom: 1px solid #f0f0f0;
                margin-bottom: 20px;
            }

            .q-pwa-tab-content h3 {
                font-size: 1.2em;
                margin: 25px 0 15px;
                padding-bottom: 10px;
                border-bottom: 1px solid #f0f0f0;
                color: #23282d;
            }

            .q-pwa-tab-content h4 {
                font-size: 1.1em;
                margin: 20px 0 10px;
                color: #23282d;
            }

            /* Form Elements */
            .q-pwa-tab-content .form-table {
                border-collapse: separate;
                border-spacing: 0 15px;
                margin-top: 0;
            }

            .q-pwa-tab-content .form-table th {
                font-weight: 600;
                padding: 15px 10px 15px 0;
                width: 200px;
                vertical-align: top;
            }

            .q-pwa-tab-content .form-table td {
                padding: 15px 10px;
                vertical-align: middle;
            }

            .q-pwa-tab-content input[type="text"],
            .q-pwa-tab-content input[type="url"],
            .q-pwa-tab-content input[type="number"],
            .q-pwa-tab-content textarea,
            .q-pwa-tab-content select {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px 12px;
                box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
                transition: border-color 0.3s ease;
                width: 100%;
                max-width: 400px;
            }

            .q-pwa-tab-content input[type="text"]:focus,
            .q-pwa-tab-content input[type="url"]:focus,
            .q-pwa-tab-content input[type="number"]:focus,
            .q-pwa-tab-content textarea:focus,
            .q-pwa-tab-content select:focus {
                border-color: #2271b1;
                box-shadow: 0 0 0 1px #2271b1;
                outline: none;
            }

            .q-pwa-tab-content input[type="color"] {
                width: 50px;
                height: 30px;
                padding: 0;
                border: 1px solid #ddd;
                border-radius: 4px;
                cursor: pointer;
            }

            .q-pwa-tab-content input[type="checkbox"] {
                border: 1px solid #ddd;
                border-radius: 3px;
                width: 18px;
                height: 18px;
            }

            .q-pwa-tab-content .description {
                color: #666;
                font-style: normal;
                margin-top: 5px;
                font-size: 13px;
            }

            /* Icons Grid */
            .q-pwa-icons-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 20px;
                margin-top: 20px;
            }

            .q-pwa-icon-field {
                background: #f9f9f9;
                padding: 15px;
                border-radius: 4px;
                border: 1px solid #eee;
                transition: all 0.3s ease;
            }

            .q-pwa-icon-field:hover {
                border-color: #ddd;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            }

            /* Shortcode Examples */
            .q-pwa-shortcode-example {
                background: #f9f9f9;
                padding: 20px;
                border-radius: 4px;
                margin-bottom: 20px;
                position: relative;
                border: 1px solid #eee;
                transition: all 0.3s ease;
            }

            .q-pwa-shortcode-example:hover {
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                border-color: #ddd;
            }

            .q-pwa-shortcode-example pre {
                margin: 0;
                padding: 0;
                white-space: pre-wrap;
                word-wrap: break-word;
                font-family: monospace;
                font-size: 14px;
                color: #333;
            }

            .q-pwa-copy-button {
                position: absolute;
                top: 10px;
                right: 10px;
                background: #fff;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px 10px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.3s ease;
                color: #555;
                display: flex;
                align-items: center;
                gap: 5px;
            }

            .q-pwa-copy-button .dashicons {
                font-size: 14px;
                width: 14px;
                height: 14px;
            }

            .q-pwa-copy-button:hover {
                background: #f0f0f0;
                border-color: #ccc;
                color: #2271b1;
            }

            .q-pwa-copy-button:hover .dashicons {
                color: #2271b1;
            }

            /* Shortcode Table */
            .q-pwa-shortcode-table {
                width: 100%;
                border-collapse: separate;
                border-spacing: 0;
                margin-top: 20px;
                border: 1px solid #eee;
                border-radius: 4px;
                overflow: hidden;
            }

            .q-pwa-shortcode-table th,
            .q-pwa-shortcode-table td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid #eee;
            }

            .q-pwa-shortcode-table th {
                background: #f5f5f5;
                font-weight: 600;
                color: #23282d;
            }

            .q-pwa-shortcode-table tr:last-child td {
                border-bottom: none;
            }

            .q-pwa-shortcode-table tr:hover td {
                background-color: #f9f9f9;
            }

            .q-pwa-shortcode-table code {
                background: #f0f0f0;
                padding: 2px 5px;
                border-radius: 3px;
                font-size: 13px;
                color: #333;
            }

            /* Preview Container */
            .q-pwa-preview-container {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 20px;
                margin-top: 20px;
                margin-bottom: 30px;
            }

            .q-pwa-preview-item {
                background: #f9f9f9;
                padding: 20px;
                border-radius: 4px;
                border: 1px solid #eee;
                text-align: center;
                transition: all 0.3s ease;
            }

            .q-pwa-preview-item:hover {
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                border-color: #ddd;
            }

            .q-pwa-preview-item h4 {
                margin-top: 0;
                margin-bottom: 15px;
                font-size: 14px;
                color: #555;
            }

            .q-pwa-preview-item button {
                padding: 8px 16px;
                font-size: 14px;
                cursor: pointer;
                display: inline-flex;
                align-items: center;
                gap: 5px;
                transition: all 0.3s ease;
            }

            .q-pwa-preview-item button.ui-button {
                background: #f0f0f0;
                border: 1px solid #ddd;
                border-radius: 4px;
                color: #333;
            }

            .q-pwa-preview-item button.ui-button.primary {
                background: #2271b1;
                border-color: #2271b1;
                color: #fff;
            }

            .q-pwa-preview-item button .dashicons {
                font-size: 16px;
                width: 16px;
                height: 16px;
            }

            /* Submit Button */
            .q-pwa-settings-wrap .submit {
                margin-top: 20px;
                padding-top: 20px;
                border-top: 1px solid #f0f0f0;
            }

            .q-pwa-settings-wrap .button-primary {
                background: #2271b1;
                border-color: #2271b1;
                color: #fff;
                padding: 5px 20px;
                height: auto;
                line-height: 2;
                font-size: 14px;
                font-weight: 500;
                border-radius: 4px;
                transition: all 0.3s ease;
            }

            .q-pwa-settings-wrap .button-primary:hover {
                background: #135e96;
                border-color: #135e96;
            }
        ');

        // Add inline script for tab functionality and copy to clipboard
        wp_add_inline_script('jquery', '
            jQuery(document).ready(function($) {
                // Tab functionality
                $(".q-pwa-tab").on("click", function() {
                    var tabId = $(this).data("tab");

                    // Update active tab
                    $(".q-pwa-tab").removeClass("active");
                    $(this).addClass("active");

                    // Show active content
                    $(".q-pwa-tab-content").removeClass("active");
                    $("#" + tabId).addClass("active");

                    // Store active tab in localStorage
                    localStorage.setItem("q_pwa_active_tab", tabId);
                });

                // Restore active tab from localStorage
                var activeTab = localStorage.getItem("q_pwa_active_tab");
                if (activeTab) {
                    $(".q-pwa-tab[data-tab=\'" + activeTab + "\']").click();
                } else {
                    // Default to first tab
                    $(".q-pwa-tab:first").click();
                }

                // Copy to clipboard functionality
                $(".q-pwa-copy-button").on("click", function() {
                    var $button = $(this);
                    var $code = $button.siblings("pre");
                    var text = $code.text();

                    // Create temporary textarea to copy from
                    var $temp = $("<textarea>");
                    $("body").append($temp);
                    $temp.val(text).select();
                    document.execCommand("copy");
                    $temp.remove();

                    // Update button text temporarily
                    var originalText = $button.text();
                    $button.text("Copied!");
                    setTimeout(function() {
                        $button.text(originalText);
                    }, 2000);
                });
            });
        ');
    }

    /**
     * Add PWA settings page
     */
    public static function add_settings_page()
    {
        add_submenu_page(
            'options-general.php',
            'Q Pusher PWA Settings',
            'Q Pusher PWA',
            'manage_options',
            'q-pusher-pwa-settings',
            [self::class, 'render_settings_page']
        );
    }

    /**
     * Register PWA settings
     */
    public static function register_settings()
    {
        // General PWA settings
        register_setting('q_pwa_settings', 'q_pwa_enabled');
        register_setting('q_pwa_settings', 'q_pwa_app_name');
        register_setting('q_pwa_settings', 'q_pwa_short_name');
        register_setting('q_pwa_settings', 'q_pwa_description');
        register_setting('q_pwa_settings', 'q_pwa_theme_color');
        register_setting('q_pwa_settings', 'q_pwa_background_color');
        register_setting('q_pwa_settings', 'q_pwa_display');

        // Icon settings
        register_setting('q_pwa_settings', 'q_pwa_icon_72');
        register_setting('q_pwa_settings', 'q_pwa_icon_96');
        register_setting('q_pwa_settings', 'q_pwa_icon_128');
        register_setting('q_pwa_settings', 'q_pwa_icon_144');
        register_setting('q_pwa_settings', 'q_pwa_icon_152');
        register_setting('q_pwa_settings', 'q_pwa_icon_192');
        register_setting('q_pwa_settings', 'q_pwa_icon_384');
        register_setting('q_pwa_settings', 'q_pwa_icon_512');

        // Cache settings
        register_setting('q_pwa_settings', 'q_pwa_offline_page');
        register_setting('q_pwa_settings', 'q_pwa_cache_urls');
        register_setting('q_pwa_settings', 'q_pwa_cache_version');

        // Installation prompt settings
        register_setting('q_pwa_settings', 'q_pwa_show_install_button');
        register_setting('q_pwa_settings', 'q_pwa_install_title');
        register_setting('q_pwa_settings', 'q_pwa_install_text');
        register_setting('q_pwa_settings', 'q_pwa_install_button_text');
        register_setting('q_pwa_settings', 'q_pwa_dismiss_button_text');
        register_setting('q_pwa_settings', 'q_pwa_dismiss_duration');

        // Floating A2HS button settings
        register_setting('q_pwa_settings', 'q_pwa_show_floating_button');
        register_setting('q_pwa_settings', 'q_pwa_floating_button_position');
        register_setting('q_pwa_settings', 'q_pwa_floating_button_color');
        register_setting('q_pwa_settings', 'q_pwa_floating_button_icon');

        // Platform-specific instructions
        register_setting('q_pwa_settings', 'q_pwa_show_instructions');
        register_setting('q_pwa_settings', 'q_pwa_custom_ios_instructions');
        register_setting('q_pwa_settings', 'q_pwa_custom_android_instructions');
        register_setting('q_pwa_settings', 'q_pwa_custom_desktop_instructions');

        // General section
        add_settings_section(
            'q_pwa_general_section',
            'General PWA Settings',
            [self::class, 'render_general_section'],
            'q_pwa_settings'
        );

        // Icons section
        add_settings_section(
            'q_pwa_icons_section',
            'PWA Icons',
            [self::class, 'render_icons_section'],
            'q_pwa_settings'
        );

        // Cache section
        add_settings_section(
            'q_pwa_cache_section',
            'Offline & Cache Settings',
            [self::class, 'render_cache_section'],
            'q_pwa_settings'
        );

        // Installation section
        add_settings_section(
            'q_pwa_install_section',
            'Installation Prompt Settings',
            [self::class, 'render_install_section'],
            'q_pwa_settings'
        );

        // General fields
        add_settings_field(
            'q_pwa_enabled',
            'Enable PWA',
            [self::class, 'render_enabled_field'],
            'q_pwa_settings',
            'q_pwa_general_section'
        );

        add_settings_field(
            'q_pwa_app_name',
            'App Name',
            [self::class, 'render_app_name_field'],
            'q_pwa_settings',
            'q_pwa_general_section'
        );

        add_settings_field(
            'q_pwa_short_name',
            'Short Name',
            [self::class, 'render_short_name_field'],
            'q_pwa_settings',
            'q_pwa_general_section'
        );

        add_settings_field(
            'q_pwa_description',
            'Description',
            [self::class, 'render_description_field'],
            'q_pwa_settings',
            'q_pwa_general_section'
        );

        add_settings_field(
            'q_pwa_theme_color',
            'Theme Color',
            [self::class, 'render_theme_color_field'],
            'q_pwa_settings',
            'q_pwa_general_section'
        );

        add_settings_field(
            'q_pwa_background_color',
            'Background Color',
            [self::class, 'render_background_color_field'],
            'q_pwa_settings',
            'q_pwa_general_section'
        );

        add_settings_field(
            'q_pwa_display',
            'Display Mode',
            [self::class, 'render_display_field'],
            'q_pwa_settings',
            'q_pwa_general_section'
        );

        // Icon fields
        add_settings_field(
            'q_pwa_icons',
            'App Icons',
            [self::class, 'render_icons_fields'],
            'q_pwa_settings',
            'q_pwa_icons_section'
        );

        // Cache fields
        add_settings_field(
            'q_pwa_offline_page',
            'Offline Page',
            [self::class, 'render_offline_page_field'],
            'q_pwa_settings',
            'q_pwa_cache_section'
        );

        add_settings_field(
            'q_pwa_cache_urls',
            'Additional URLs to Cache',
            [self::class, 'render_cache_urls_field'],
            'q_pwa_settings',
            'q_pwa_cache_section'
        );

        add_settings_field(
            'q_pwa_cache_version',
            'Cache Version',
            [self::class, 'render_cache_version_field'],
            'q_pwa_settings',
            'q_pwa_cache_section'
        );

        // Installation fields
        add_settings_field(
            'q_pwa_show_install_button',
            'Show Install Button',
            [self::class, 'render_show_install_button_field'],
            'q_pwa_settings',
            'q_pwa_install_section'
        );

        add_settings_field(
            'q_pwa_install_title',
            'Install Prompt Title',
            [self::class, 'render_install_title_field'],
            'q_pwa_settings',
            'q_pwa_install_section'
        );

        add_settings_field(
            'q_pwa_install_text',
            'Install Prompt Text',
            [self::class, 'render_install_text_field'],
            'q_pwa_settings',
            'q_pwa_install_section'
        );

        add_settings_field(
            'q_pwa_install_button_text',
            'Install Button Text',
            [self::class, 'render_install_button_text_field'],
            'q_pwa_settings',
            'q_pwa_install_section'
        );

        add_settings_field(
            'q_pwa_dismiss_button_text',
            'Dismiss Button Text',
            [self::class, 'render_dismiss_button_text_field'],
            'q_pwa_settings',
            'q_pwa_install_section'
        );

        add_settings_field(
            'q_pwa_dismiss_duration',
            'Dismiss Duration (hours)',
            [self::class, 'render_dismiss_duration_field'],
            'q_pwa_settings',
            'q_pwa_install_section'
        );

        // Floating button fields
        add_settings_field(
            'q_pwa_show_floating_button',
            'Show Floating Install Button',
            [self::class, 'render_show_floating_button_field'],
            'q_pwa_settings',
            'q_pwa_install_section'
        );

        add_settings_field(
            'q_pwa_floating_button_position',
            'Floating Button Position',
            [self::class, 'render_floating_button_position_field'],
            'q_pwa_settings',
            'q_pwa_install_section'
        );

        add_settings_field(
            'q_pwa_floating_button_color',
            'Floating Button Color',
            [self::class, 'render_floating_button_color_field'],
            'q_pwa_settings',
            'q_pwa_install_section'
        );

        // Platform-specific instructions fields
        add_settings_field(
            'q_pwa_show_instructions',
            'Show Platform Instructions',
            [self::class, 'render_show_instructions_field'],
            'q_pwa_settings',
            'q_pwa_install_section'
        );

        add_settings_field(
            'q_pwa_custom_ios_instructions',
            'iOS Instructions',
            [self::class, 'render_custom_ios_instructions_field'],
            'q_pwa_settings',
            'q_pwa_install_section'
        );

        add_settings_field(
            'q_pwa_custom_android_instructions',
            'Android Instructions',
            [self::class, 'render_custom_android_instructions_field'],
            'q_pwa_settings',
            'q_pwa_install_section'
        );

        add_settings_field(
            'q_pwa_custom_desktop_instructions',
            'Desktop Instructions',
            [self::class, 'render_custom_desktop_instructions_field'],
            'q_pwa_settings',
            'q_pwa_install_section'
        );
    }

    /**
     * Render settings page
     */
    public static function render_settings_page()
    {
        ?>
        <div class="wrap q-pwa-settings-wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

            <div class="q-pwa-tabs">
                <div class="q-pwa-tab active" data-tab="tab-general">
                    <span class="dashicons dashicons-admin-settings"></span> General
                </div>
                <div class="q-pwa-tab" data-tab="tab-icons">
                    <span class="dashicons dashicons-format-image"></span> Icons & Appearance
                </div>
                <div class="q-pwa-tab" data-tab="tab-offline">
                    <span class="dashicons dashicons-cloud"></span> Offline & Cache
                </div>
                <div class="q-pwa-tab" data-tab="tab-install">
                    <span class="dashicons dashicons-download"></span> Installation Prompts
                </div>
                <div class="q-pwa-tab" data-tab="tab-shortcodes">
                    <span class="dashicons dashicons-shortcode"></span> Shortcodes & Usage
                </div>
            </div>

            <form action="options.php" method="post">
                <?php settings_fields('q_pwa_settings'); ?>

                <!-- General Tab -->
                <div id="tab-general" class="q-pwa-tab-content active">
                    <h2>General PWA Settings</h2>
                    <p>Configure the basic settings for your Progressive Web App.</p>
                    <table class="form-table">
                        <?php
                        self::render_enabled_field_row();
                        self::render_app_name_field_row();
                        self::render_short_name_field_row();
                        self::render_description_field_row();
                        self::render_theme_color_field_row();
                        self::render_background_color_field_row();
                        self::render_display_field_row();
                        ?>
                    </table>
                </div>

                <!-- Icons Tab -->
                <div id="tab-icons" class="q-pwa-tab-content">
                    <h2>PWA Icons & Appearance</h2>
                    <p>Upload icons for your PWA. These will be used on the home screen and app launcher.</p>
                    <?php self::render_icons_fields(); ?>
                </div>

                <!-- Offline Tab -->
                <div id="tab-offline" class="q-pwa-tab-content">
                    <h2>Offline & Cache Settings</h2>
                    <p>Configure offline functionality and caching for your PWA.</p>
                    <table class="form-table">
                        <?php
                        self::render_offline_page_field_row();
                        self::render_cache_urls_field_row();
                        self::render_cache_version_field_row();
                        ?>
                    </table>
                </div>

                <!-- Installation Tab -->
                <div id="tab-install" class="q-pwa-tab-content">
                    <h2>Installation Prompt Settings</h2>
                    <p>Configure how the PWA installation prompt appears to users.</p>

                    <h3>Standard Install Prompt</h3>
                    <table class="form-table">
                        <?php
                        self::render_show_install_button_field_row();
                        self::render_install_title_field_row();
                        self::render_install_text_field_row();
                        self::render_install_button_text_field_row();
                        self::render_dismiss_button_text_field_row();
                        self::render_dismiss_duration_field_row();
                        ?>
                    </table>

                    <h3>Floating Install Button</h3>
                    <table class="form-table">
                        <?php
                        self::render_show_floating_button_field_row();
                        self::render_floating_button_position_field_row();
                        self::render_floating_button_color_field_row();
                        ?>
                    </table>

                    <h3>Platform-Specific Instructions</h3>
                    <table class="form-table">
                        <?php
                        self::render_show_instructions_field_row();
                        self::render_custom_ios_instructions_field_row();
                        self::render_custom_android_instructions_field_row();
                        self::render_custom_desktop_instructions_field_row();
                        ?>
                    </table>
                </div>

                <!-- Shortcodes Tab -->
                <div id="tab-shortcodes" class="q-pwa-tab-content">
                    <h2>Shortcodes & Usage</h2>
                    <p>Use these shortcodes to add PWA functionality to your site.</p>

                    <h3>Add to Home Screen Button</h3>
                    <p>Use the <code>[q_add_to_home]</code> shortcode to display an "Add to Home Screen" button on your site.
                    </p>

                    <div class="q-pwa-shortcode-example">
                        <button class="q-pwa-copy-button"><span class="dashicons dashicons-clipboard"></span> Copy</button>
                        <pre>[q_add_to_home]</pre>
                    </div>

                    <h4>Shortcode Parameters</h4>
                    <p>Customize the button appearance and behavior with these parameters:</p>

                    <table class="q-pwa-shortcode-table">
                        <thead>
                            <tr>
                                <th>Parameter</th>
                                <th>Default</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>text</code></td>
                                <td>"Add to Home Screen"</td>
                                <td>The text displayed on the button</td>
                            </tr>
                            <tr>
                                <td><code>class</code></td>
                                <td>"ui-button primary"</td>
                                <td>CSS classes to apply to the button</td>
                            </tr>
                            <tr>
                                <td><code>icon</code></td>
                                <td>true</td>
                                <td>Whether to show an icon on the button (true/false)</td>
                            </tr>
                            <tr>
                                <td><code>style</code></td>
                                <td>"default"</td>
                                <td>Button style: "default", "pill", "flat", or "outline"</td>
                            </tr>
                            <tr>
                                <td><code>color</code></td>
                                <td>""</td>
                                <td>Custom button color (hex code)</td>
                            </tr>
                            <tr>
                                <td><code>show_instructions</code></td>
                                <td>true</td>
                                <td>Show platform-specific instructions (true/false)</td>
                            </tr>
                            <tr>
                                <td><code>size</code></td>
                                <td>"medium"</td>
                                <td>Button size: "small", "medium", or "large"</td>
                            </tr>
                            <tr>
                                <td><code>full_width</code></td>
                                <td>false</td>
                                <td>Make button full width (true/false)</td>
                            </tr>
                            <tr>
                                <td><code>align</code></td>
                                <td>"left"</td>
                                <td>Button alignment: "left", "center", or "right"</td>
                            </tr>
                            <tr>
                                <td><code>custom_id</code></td>
                                <td>""</td>
                                <td>Custom ID for the button</td>
                            </tr>
                        </tbody>
                    </table>

                    <h4>Examples</h4>

                    <p><strong>Basic button:</strong></p>
                    <div class="q-pwa-shortcode-example">
                        <button class="q-pwa-copy-button"><span class="dashicons dashicons-clipboard"></span> Copy</button>
                        <pre>[q_add_to_home]</pre>
                    </div>

                    <p><strong>Customized button:</strong></p>
                    <div class="q-pwa-shortcode-example">
                        <button class="q-pwa-copy-button"><span class="dashicons dashicons-clipboard"></span> Copy</button>
                        <pre>[q_add_to_home text="Install Our App" style="pill" color="#ff5722" size="large" align="center"]</pre>
                    </div>

                    <p><strong>Button without instructions:</strong></p>
                    <div class="q-pwa-shortcode-example">
                        <button class="q-pwa-copy-button"><span class="dashicons dashicons-clipboard"></span> Copy</button>
                        <pre>[q_add_to_home show_instructions="false" text="Add App" style="outline"]</pre>
                    </div>

                    <h3>Live Preview</h3>
                    <p>Here's how the Add to Home Screen button will look with different styles:</p>

                    <div class="q-pwa-preview-container">
                        <div class="q-pwa-preview-item">
                            <h4>Default Style</h4>
                            <button class="ui-button primary">
                                <span class="dashicons dashicons-download"></span> Add to Home Screen
                            </button>
                        </div>

                        <div class="q-pwa-preview-item">
                            <h4>Pill Style</h4>
                            <button class="ui-button primary" style="border-radius: 50px;">
                                <span class="dashicons dashicons-download"></span> Add to Home Screen
                            </button>
                        </div>

                        <div class="q-pwa-preview-item">
                            <h4>Outline Style</h4>
                            <button class="ui-button"
                                style="background: transparent; border: 2px solid #2271b1; color: #2271b1;">
                                <span class="dashicons dashicons-download"></span> Add to Home Screen
                            </button>
                        </div>

                        <div class="q-pwa-preview-item">
                            <h4>Flat Style</h4>
                            <button class="ui-button primary" style="border-radius: 0; box-shadow: none;">
                                <span class="dashicons dashicons-download"></span> Add to Home Screen
                            </button>
                        </div>
                    </div>
                </div>

                <?php submit_button('Save Settings'); ?>
            </form>
        </div>
        <?php
    }

    /**
     * Helper methods to render form fields with table rows
     */
    public static function render_enabled_field_row()
    {
        ?>
        <tr>
            <th scope="row">Enable PWA</th>
            <td><?php self::render_enabled_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_app_name_field_row()
    {
        ?>
        <tr>
            <th scope="row">App Name</th>
            <td><?php self::render_app_name_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_short_name_field_row()
    {
        ?>
        <tr>
            <th scope="row">Short Name</th>
            <td><?php self::render_short_name_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_description_field_row()
    {
        ?>
        <tr>
            <th scope="row">Description</th>
            <td><?php self::render_description_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_theme_color_field_row()
    {
        ?>
        <tr>
            <th scope="row">Theme Color</th>
            <td><?php self::render_theme_color_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_background_color_field_row()
    {
        ?>
        <tr>
            <th scope="row">Background Color</th>
            <td><?php self::render_background_color_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_display_field_row()
    {
        ?>
        <tr>
            <th scope="row">Display Mode</th>
            <td><?php self::render_display_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_offline_page_field_row()
    {
        ?>
        <tr>
            <th scope="row">Offline Page</th>
            <td><?php self::render_offline_page_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_cache_urls_field_row()
    {
        ?>
        <tr>
            <th scope="row">Additional URLs to Cache</th>
            <td><?php self::render_cache_urls_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_cache_version_field_row()
    {
        ?>
        <tr>
            <th scope="row">Cache Version</th>
            <td><?php self::render_cache_version_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_show_install_button_field_row()
    {
        ?>
        <tr>
            <th scope="row">Show Install Button</th>
            <td><?php self::render_show_install_button_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_install_title_field_row()
    {
        ?>
        <tr>
            <th scope="row">Install Prompt Title</th>
            <td><?php self::render_install_title_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_install_text_field_row()
    {
        ?>
        <tr>
            <th scope="row">Install Prompt Text</th>
            <td><?php self::render_install_text_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_install_button_text_field_row()
    {
        ?>
        <tr>
            <th scope="row">Install Button Text</th>
            <td><?php self::render_install_button_text_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_dismiss_button_text_field_row()
    {
        ?>
        <tr>
            <th scope="row">Dismiss Button Text</th>
            <td><?php self::render_dismiss_button_text_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_dismiss_duration_field_row()
    {
        ?>
        <tr>
            <th scope="row">Dismiss Duration (hours)</th>
            <td><?php self::render_dismiss_duration_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_show_floating_button_field_row()
    {
        ?>
        <tr>
            <th scope="row">Show Floating Install Button</th>
            <td><?php self::render_show_floating_button_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_floating_button_position_field_row()
    {
        ?>
        <tr>
            <th scope="row">Floating Button Position</th>
            <td><?php self::render_floating_button_position_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_floating_button_color_field_row()
    {
        ?>
        <tr>
            <th scope="row">Floating Button Color</th>
            <td><?php self::render_floating_button_color_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_show_instructions_field_row()
    {
        ?>
        <tr>
            <th scope="row">Show Platform Instructions</th>
            <td><?php self::render_show_instructions_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_custom_ios_instructions_field_row()
    {
        ?>
        <tr>
            <th scope="row">iOS Instructions</th>
            <td><?php self::render_custom_ios_instructions_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_custom_android_instructions_field_row()
    {
        ?>
        <tr>
            <th scope="row">Android Instructions</th>
            <td><?php self::render_custom_android_instructions_field(); ?></td>
        </tr>
        <?php
    }

    public static function render_custom_desktop_instructions_field_row()
    {
        ?>
        <tr>
            <th scope="row">Desktop Instructions</th>
            <td><?php self::render_custom_desktop_instructions_field(); ?></td>
        </tr>
        <?php
    }

    /**
     * Render general section
     */
    public static function render_general_section()
    {
        echo '<p>Configure the basic settings for your Progressive Web App.</p>';
    }

    /**
     * Render icons section
     */
    public static function render_icons_section()
    {
        echo '<p>Upload icons for your PWA. These will be used on the home screen and app launcher.</p>';
    }

    /**
     * Render cache section
     */
    public static function render_cache_section()
    {
        echo '<p>Configure offline functionality and caching for your PWA.</p>';
    }

    /**
     * Render enabled field
     */
    public static function render_enabled_field()
    {
        $enabled = get_option('q_pwa_enabled', false);
        ?>
        <label>
            <input type="checkbox" name="q_pwa_enabled" value="1" <?php checked(1, $enabled); ?>>
            Enable Progressive Web App functionality
        </label>
        <p class="description">This will add the necessary components to make your site installable as a PWA.</p>
        <?php
    }

    /**
     * Render app name field
     */
    public static function render_app_name_field()
    {
        $app_name = get_option('q_pwa_app_name', get_bloginfo('name'));
        ?>
        <input type="text" name="q_pwa_app_name" value="<?php echo esc_attr($app_name); ?>" class="regular-text">
        <p class="description">The full name of your application as it will appear on the home screen.</p>
        <?php
    }

    /**
     * Render short name field
     */
    public static function render_short_name_field()
    {
        $short_name = get_option('q_pwa_short_name', get_bloginfo('name'));
        ?>
        <input type="text" name="q_pwa_short_name" value="<?php echo esc_attr($short_name); ?>" class="regular-text">
        <p class="description">A shorter name used when there isn't enough space (max 12 characters recommended).</p>
        <?php
    }

    /**
     * Render description field
     */
    public static function render_description_field()
    {
        $description = get_option('q_pwa_description', get_bloginfo('description'));
        ?>
        <textarea name="q_pwa_description" rows="3" class="large-text"><?php echo esc_textarea($description); ?></textarea>
        <p class="description">A brief description of your application.</p>
        <?php
    }

    /**
     * Render theme color field
     */
    public static function render_theme_color_field()
    {
        $theme_color = get_option('q_pwa_theme_color', '#ffffff');
        ?>
        <input type="color" name="q_pwa_theme_color" value="<?php echo esc_attr($theme_color); ?>">
        <p class="description">The theme color for your application. This affects how the OS displays the site (e.g., in the
            task switcher).</p>
        <?php
    }

    /**
     * Render background color field
     */
    public static function render_background_color_field()
    {
        $background_color = get_option('q_pwa_background_color', '#ffffff');
        ?>
        <input type="color" name="q_pwa_background_color" value="<?php echo esc_attr($background_color); ?>">
        <p class="description">The background color for your application, used during the splash screen.</p>
        <?php
    }

    /**
     * Render display field
     */
    public static function render_display_field()
    {
        $display = get_option('q_pwa_display', 'standalone');
        ?>
        <select name="q_pwa_display">
            <option value="fullscreen" <?php selected('fullscreen', $display); ?>>Fullscreen</option>
            <option value="standalone" <?php selected('standalone', $display); ?>>Standalone</option>
            <option value="minimal-ui" <?php selected('minimal-ui', $display); ?>>Minimal UI</option>
            <option value="browser" <?php selected('browser', $display); ?>>Browser</option>
        </select>
        <p class="description">How the application should be displayed. Standalone is recommended for most PWAs.</p>
        <?php
    }

    /**
     * Render icons fields
     */
    public static function render_icons_fields()
    {
        $icon_sizes = [
            '72' => '72x72 (Android/Chrome)',
            '96' => '96x96 (Android/Chrome)',
            '128' => '128x128 (Chrome Web Store)',
            '144' => '144x144 (Android/Chrome)',
            '152' => '152x152 (iOS)',
            '192' => '192x192 (Android/Chrome)',
            '384' => '384x384 (Android/Chrome)',
            '512' => '512x512 (Android/Chrome)'
        ];

        echo '<p>Upload PNG icons for your PWA in various sizes:</p>';
        echo '<div class="q-pwa-icons-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px;">';

        foreach ($icon_sizes as $size => $label) {
            $option_name = 'q_pwa_icon_' . $size;
            $icon_url = get_option($option_name, '');

            echo '<div class="q-pwa-icon-field" style="margin-bottom: 15px;">';
            echo '<label><strong>' . esc_html($label) . '</strong></label><br>';
            echo '<input type="url" name="' . esc_attr($option_name) . '" value="' . esc_url($icon_url) . '" class="regular-text" placeholder="https://...">';
            echo '</div>';
        }

        echo '</div>';
        echo '<p class="description">Enter the full URL to each icon. For best results, use PNG images with transparent backgrounds.</p>';
    }

    /**
     * Render offline page field
     */
    public static function render_offline_page_field()
    {
        $offline_page = get_option('q_pwa_offline_page', home_url('/'));
        ?>
        <input type="url" name="q_pwa_offline_page" value="<?php echo esc_url($offline_page); ?>" class="regular-text">
        <p class="description">The URL to show when the user is offline. This page will be cached for offline access.</p>
        <?php
    }

    /**
     * Render cache URLs field
     */
    public static function render_cache_urls_field()
    {
        $cache_urls = get_option('q_pwa_cache_urls', '');
        ?>
        <textarea name="q_pwa_cache_urls" rows="5" class="large-text"><?php echo esc_textarea($cache_urls); ?></textarea>
        <p class="description">Enter additional URLs to cache for offline access, one per line. The home page and offline page
            are cached automatically.</p>
        <?php
    }

    /**
     * Render cache version field
     */
    public static function render_cache_version_field()
    {
        $cache_version = get_option('q_pwa_cache_version', '1.0.0');
        ?>
        <input type="text" name="q_pwa_cache_version" value="<?php echo esc_attr($cache_version); ?>" class="regular-text">
        <p class="description">Increment this version when you want to force a refresh of the cached content.</p>
        <?php
    }

    /**
     * Render installation section
     */
    public static function render_install_section()
    {
        echo '<p>Configure how the PWA installation prompt appears to users.</p>';
    }

    /**
     * Render show install button field
     */
    public static function render_show_install_button_field()
    {
        $show_install_button = get_option('q_pwa_show_install_button', true);
        ?>
        <label>
            <input type="checkbox" name="q_pwa_show_install_button" value="1" <?php checked(1, $show_install_button); ?>>
            Show custom install button when the app can be installed
        </label>
        <p class="description">When enabled, a custom install button will be shown to users who can install the PWA.</p>
        <?php
    }

    /**
     * Render install title field
     */
    public static function render_install_title_field()
    {
        $install_title = get_option('q_pwa_install_title', 'Install App');
        ?>
        <input type="text" name="q_pwa_install_title" value="<?php echo esc_attr($install_title); ?>" class="regular-text">
        <p class="description">The title shown in the installation prompt.</p>
        <?php
    }

    /**
     * Render install text field
     */
    public static function render_install_text_field()
    {
        $install_text = get_option('q_pwa_install_text', 'Install this app on your device for quick and easy access.');
        ?>
        <textarea name="q_pwa_install_text" rows="2" class="large-text"><?php echo esc_textarea($install_text); ?></textarea>
        <p class="description">The description text shown in the installation prompt.</p>
        <?php
    }

    /**
     * Render install button text field
     */
    public static function render_install_button_text_field()
    {
        $button_text = get_option('q_pwa_install_button_text', 'Install');
        ?>
        <input type="text" name="q_pwa_install_button_text" value="<?php echo esc_attr($button_text); ?>" class="regular-text">
        <p class="description">The text shown on the install button.</p>
        <?php
    }

    /**
     * Render dismiss button text field
     */
    public static function render_dismiss_button_text_field()
    {
        $dismiss_text = get_option('q_pwa_dismiss_button_text', 'Not Now');
        ?>
        <input type="text" name="q_pwa_dismiss_button_text" value="<?php echo esc_attr($dismiss_text); ?>" class="regular-text">
        <p class="description">The text shown on the dismiss button.</p>
        <?php
    }

    /**
     * Render dismiss duration field
     */
    public static function render_dismiss_duration_field()
    {
        $dismiss_duration = get_option('q_pwa_dismiss_duration', 86400000) / 3600000; // Convert from ms to hours
        ?>
        <input type="number" name="q_pwa_dismiss_duration" value="<?php echo esc_attr($dismiss_duration); ?>" class="small-text"
            min="0" step="1">
        <p class="description">How long to wait before showing the install prompt again after a user dismisses it (in hours).
            Set to 0 to show it every time.</p>
        <?php
    }

    /**
     * Render show floating button field
     */
    public static function render_show_floating_button_field()
    {
        $show_floating_button = get_option('q_pwa_show_floating_button', false);
        ?>
        <label>
            <input type="checkbox" name="q_pwa_show_floating_button" value="1" <?php checked(1, $show_floating_button); ?>>
            Show a floating Add to Home Screen button
        </label>
        <p class="description">When enabled, a floating button will appear on the screen when the app can be installed.</p>
        <?php
    }

    /**
     * Render floating button position field
     */
    public static function render_floating_button_position_field()
    {
        $position = get_option('q_pwa_floating_button_position', 'bottom-right');
        ?>
        <select name="q_pwa_floating_button_position">
            <option value="bottom-right" <?php selected('bottom-right', $position); ?>>Bottom Right</option>
            <option value="bottom-left" <?php selected('bottom-left', $position); ?>>Bottom Left</option>
            <option value="top-right" <?php selected('top-right', $position); ?>>Top Right</option>
            <option value="top-left" <?php selected('top-left', $position); ?>>Top Left</option>
        </select>
        <p class="description">The position of the floating button on the screen.</p>
        <?php
    }

    /**
     * Render floating button color field
     */
    public static function render_floating_button_color_field()
    {
        $color = get_option('q_pwa_floating_button_color', '#0384c6');
        ?>
        <input type="color" name="q_pwa_floating_button_color" value="<?php echo esc_attr($color); ?>">
        <p class="description">The color of the floating button.</p>
        <?php
    }

    /**
     * Render show instructions field
     */
    public static function render_show_instructions_field()
    {
        $show_instructions = get_option('q_pwa_show_instructions', true);
        ?>
        <label>
            <input type="checkbox" name="q_pwa_show_instructions" value="1" <?php checked(1, $show_instructions); ?>>
            Show platform-specific installation instructions
        </label>
        <p class="description">When enabled, users will see instructions specific to their device (iOS, Android, Desktop).</p>
        <?php
    }

    /**
     * Render custom iOS instructions field
     */
    public static function render_custom_ios_instructions_field()
    {
        $default_instructions = "To install this app on your iOS device:\n1. Tap the Share icon (rectangle with arrow) at the bottom of the screen\n2. Scroll down and tap 'Add to Home Screen'\n3. Tap 'Add' in the top right corner";
        $instructions = get_option('q_pwa_custom_ios_instructions', $default_instructions);
        ?>
        <textarea name="q_pwa_custom_ios_instructions" rows="4"
            class="large-text"><?php echo esc_textarea($instructions); ?></textarea>
        <p class="description">Custom instructions for iOS users. These will be shown when a user on iOS attempts to install the
            app.</p>
        <?php
    }

    /**
     * Render custom Android instructions field
     */
    public static function render_custom_android_instructions_field()
    {
        $default_instructions = "To install this app on your Android device:\n1. Tap the 'Install' button in the prompt\n2. If no prompt appears, tap the menu icon (three dots) in your browser\n3. Select 'Add to Home screen' or 'Install App'";
        $instructions = get_option('q_pwa_custom_android_instructions', $default_instructions);
        ?>
        <textarea name="q_pwa_custom_android_instructions" rows="4"
            class="large-text"><?php echo esc_textarea($instructions); ?></textarea>
        <p class="description">Custom instructions for Android users. These will be shown when a user on Android attempts to
            install the app.</p>
        <?php
    }

    /**
     * Render custom desktop instructions field
     */
    public static function render_custom_desktop_instructions_field()
    {
        $default_instructions = "To install this app on your desktop:\n1. Click the install icon in the address bar\n2. Click 'Install' in the prompt that appears\n3. The app will open in a new window";
        $instructions = get_option('q_pwa_custom_desktop_instructions', $default_instructions);
        ?>
        <textarea name="q_pwa_custom_desktop_instructions" rows="4"
            class="large-text"><?php echo esc_textarea($instructions); ?></textarea>
        <p class="description">Custom instructions for desktop users. These will be shown when a user on desktop attempts to
            install the app.</p>
        <?php
    }
}
