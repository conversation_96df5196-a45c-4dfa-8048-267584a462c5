/**
 * Q-<PERSON>usher PWA Registration Script
 *
 * This script handles the registration of the PWA service worker,
 * provides update functionality, and manages installation prompts.
 */

// Store the install prompt event for later use
let deferredPrompt;

// Store the detected platform
let detectedPlatform = 'unknown';

// Check if service workers are supported
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        registerServiceWorker();
        detectPlatform();
    });
}

/**
 * Detect the user's platform (iOS, Android, Desktop)
 */
function detectPlatform() {
    const ua = navigator.userAgent.toLowerCase();

    if (/iphone|ipad|ipod/.test(ua)) {
        detectedPlatform = 'ios';
    } else if (/android/.test(ua)) {
        detectedPlatform = 'android';
    } else {
        detectedPlatform = 'desktop';
    }

    // Add platform class to body
    document.body.classList.add('q-platform-' + detectedPlatform);
}

// Listen for the beforeinstallprompt event
window.addEventListener('beforeinstallprompt', (e) => {
    // Prevent the default browser install prompt
    e.preventDefault();

    // Store the event for later use
    deferredPrompt = e;

    // Show custom install button if enabled in settings
    if (qPwaData.showInstallButton) {
        showInstallButton();
    }

    // Show floating button if enabled
    if (qPwaData.showFloatingButton) {
        showFloatingInstallButton();
    }
});

/**
 * Register the service worker
 */
async function registerServiceWorker() {
    try {
        // Get the service worker URL from the localized data
        const swUrl = qPwaData.serviceWorkerUrl || '/q-sw.js';

        // Register the service worker
        const registration = await navigator.serviceWorker.register(swUrl, {
            scope: '/'
        });

        if (qPwaData.debug) {
            console.log('Q-PWA: Service Worker registered with scope:', registration.scope);
        }

        // Check for updates
        registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;

            if (qPwaData.debug) {
                console.log('Q-PWA: New service worker installing');
            }

            newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                    // New content is available, show update notification
                    showUpdateNotification();
                }
            });
        });

        // Handle controller change
        navigator.serviceWorker.addEventListener('controllerchange', () => {
            if (qPwaData.debug) {
                console.log('Q-PWA: New service worker activated');
            }
        });

    } catch (error) {
        console.error('Q-PWA: Service Worker registration failed:', error);
    }
}

/**
 * Show update notification
 */
function showUpdateNotification() {
    // Create notification container if it doesn't exist
    let container = document.getElementById('q-pwa-update-notification');

    if (!container) {
        container = document.createElement('div');
        container.id = 'q-pwa-update-notification';
        container.style.position = 'fixed';
        container.style.bottom = '20px';
        container.style.right = '20px';
        container.style.backgroundColor = '#ffffff';
        container.style.color = '#333333';
        container.style.padding = '15px';
        container.style.borderRadius = '5px';
        container.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
        container.style.zIndex = '9999';
        container.style.display = 'flex';
        container.style.alignItems = 'center';
        container.style.justifyContent = 'space-between';
        container.style.maxWidth = '400px';

        document.body.appendChild(container);
    }

    // Set notification content
    container.innerHTML = `
        <div style="margin-right: 15px;">
            <strong>Update Available</strong>
            <p style="margin: 5px 0 0;">A new version of this app is available.</p>
        </div>
        <button id="q-pwa-update-button" style="background-color: #0384c6; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">
            Update
        </button>
    `;

    // Add update button event listener
    document.getElementById('q-pwa-update-button').addEventListener('click', () => {
        // Send message to service worker to skip waiting
        navigator.serviceWorker.controller.postMessage({ type: 'SKIP_WAITING' });

        // Remove notification
        container.remove();
    });
}

/**
 * Check if the app is in standalone mode (installed PWA)
 */
function isInStandaloneMode() {
    return (window.matchMedia('(display-mode: standalone)').matches) ||
           (window.navigator.standalone) ||
           document.referrer.includes('android-app://');
}

// Add a class to the body if running as installed PWA
if (isInStandaloneMode()) {
    document.body.classList.add('q-pwa-standalone');
}

/**
 * Show the install button
 */
function showInstallButton() {
    // Check if button already exists
    if (document.getElementById('q-pwa-install-button')) {
        return;
    }

    // Check if the app was recently dismissed
    const dismissedTime = localStorage.getItem('q-pwa-install-dismissed');
    if (dismissedTime && qPwaData.dismissDuration) {
        const dismissDuration = parseInt(qPwaData.dismissDuration, 10);
        const currentTime = Date.now();
        const dismissedAt = parseInt(dismissedTime, 10);

        // If the dismissal duration hasn't passed yet, don't show the button
        if (currentTime - dismissedAt < dismissDuration) {
            return;
        }
    }

    // Create button container
    const container = document.createElement('div');
    container.id = 'q-pwa-install-container';
    container.style.position = 'fixed';
    container.style.bottom = '20px';
    container.style.left = '20px';
    container.style.backgroundColor = '#ffffff';
    container.style.color = '#333333';
    container.style.padding = '15px';
    container.style.borderRadius = '5px';
    container.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
    container.style.zIndex = '9999';
    container.style.display = 'flex';
    container.style.alignItems = 'center';
    container.style.justifyContent = 'space-between';
    container.style.maxWidth = '400px';
    container.style.animation = 'q-pwa-slide-in 0.3s ease-out';

    // Set container content
    container.innerHTML = `
        <div style="margin-right: 15px;">
            <strong>${qPwaData.installTitle || 'Install App'}</strong>
            <p style="margin: 5px 0 0;">${qPwaData.installText || 'Install this app on your device for quick and easy access.'}</p>
        </div>
        <div>
            <button id="q-pwa-install-button" style="background-color: #0384c6; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-right: 5px;">
                ${qPwaData.installButtonText || 'Install'}
            </button>
            <button id="q-pwa-install-dismiss" style="background-color: #f1f1f1; color: #333; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">
                ${qPwaData.dismissButtonText || 'Not Now'}
            </button>
        </div>
    `;

    // Add to document
    document.body.appendChild(container);

    // Add event listeners
    document.getElementById('q-pwa-install-button').addEventListener('click', () => {
        installApp();

        // Show platform-specific instructions if enabled
        if (qPwaData.showInstructions) {
            showPlatformInstructions();
        }
    });

    document.getElementById('q-pwa-install-dismiss').addEventListener('click', () => {
        container.remove();

        // Store dismissal in localStorage to avoid showing again for a while
        if (qPwaData.dismissDuration) {
            localStorage.setItem('q-pwa-install-dismissed', Date.now().toString());
        }
    });

    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes q-pwa-slide-in {
            from { transform: translateY(100px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        @keyframes q-pwa-pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    `;
    document.head.appendChild(style);
}

/**
 * Show floating install button
 */
function showFloatingInstallButton() {
    // Check if button already exists
    if (document.getElementById('q-pwa-floating-button')) {
        return;
    }

    // Check if the app was recently dismissed
    const dismissedTime = localStorage.getItem('q-pwa-install-dismissed');
    if (dismissedTime && qPwaData.dismissDuration) {
        const dismissDuration = parseInt(qPwaData.dismissDuration, 10);
        const currentTime = Date.now();
        const dismissedAt = parseInt(dismissedTime, 10);

        // If the dismissal duration hasn't passed yet, don't show the button
        if (currentTime - dismissedAt < dismissDuration) {
            return;
        }
    }

    // Create floating button
    const button = document.createElement('button');
    button.id = 'q-pwa-floating-button';
    button.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 3v12m0 0 4-4m-4 4-4-4M5 21h14"></path></svg>';

    // Set position based on settings
    const position = qPwaData.floatingButtonPosition || 'bottom-right';
    button.style.position = 'fixed';

    if (position.includes('bottom')) {
        button.style.bottom = '20px';
    } else {
        button.style.top = '20px';
    }

    if (position.includes('right')) {
        button.style.right = '20px';
    } else {
        button.style.left = '20px';
    }

    // Set other styles
    button.style.backgroundColor = qPwaData.floatingButtonColor || '#0384c6';
    button.style.color = 'white';
    button.style.border = 'none';
    button.style.borderRadius = '50%';
    button.style.width = '56px';
    button.style.height = '56px';
    button.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
    button.style.cursor = 'pointer';
    button.style.zIndex = '9998';
    button.style.display = 'flex';
    button.style.alignItems = 'center';
    button.style.justifyContent = 'center';
    button.style.animation = 'q-pwa-pulse 2s infinite';

    // Add tooltip
    button.title = 'Add to Home Screen';

    // Add to document
    document.body.appendChild(button);

    // Add event listener
    button.addEventListener('click', () => {
        installApp();

        // Show platform-specific instructions if enabled
        if (qPwaData.showInstructions) {
            showPlatformInstructions();
        }
    });
}

/**
 * Show platform-specific installation instructions
 */
function showPlatformInstructions() {
    // Don't show instructions if not enabled or if the prompt is available
    if (!qPwaData.showInstructions || (deferredPrompt && detectedPlatform !== 'ios')) {
        return;
    }

    // Get instructions based on platform
    let instructions = '';
    let title = 'Installation Instructions';

    switch (detectedPlatform) {
        case 'ios':
            instructions = qPwaData.iosInstructions;
            title = 'Install on iOS';
            break;
        case 'android':
            instructions = qPwaData.androidInstructions;
            title = 'Install on Android';
            break;
        case 'desktop':
            instructions = qPwaData.desktopInstructions;
            title = 'Install on Desktop';
            break;
        default:
            return; // Don't show instructions for unknown platforms
    }

    // Create instructions modal
    const modal = document.createElement('div');
    modal.id = 'q-pwa-instructions-modal';
    modal.style.position = 'fixed';
    modal.style.top = '0';
    modal.style.left = '0';
    modal.style.width = '100%';
    modal.style.height = '100%';
    modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    modal.style.zIndex = '10000';
    modal.style.display = 'flex';
    modal.style.alignItems = 'center';
    modal.style.justifyContent = 'center';

    // Create modal content
    const modalContent = document.createElement('div');
    modalContent.style.backgroundColor = 'white';
    modalContent.style.borderRadius = '8px';
    modalContent.style.padding = '20px';
    modalContent.style.maxWidth = '500px';
    modalContent.style.width = '90%';
    modalContent.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';

    // Add platform icon
    let platformIcon = '';
    switch (detectedPlatform) {
        case 'ios':
            platformIcon = '<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2a8 8 0 0 0-8 8v12h16V10a8 8 0 0 0-8-8z"></path><path d="M4 10v2a8 8 0 0 0 16 0v-2"></path><path d="M8 16v2"></path><path d="M16 16v2"></path></svg>';
            break;
        case 'android':
            platformIcon = '<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M5 16V8a7 7 0 0 1 14 0v8"></path><path d="M9 18h6"></path><path d="M3 16h18"></path><path d="M12 8v2"></path><path d="M8 2v2"></path><path d="M16 2v2"></path></svg>';
            break;
        default:
            platformIcon = '<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect><line x1="8" y1="21" x2="16" y2="21"></line><line x1="12" y1="17" x2="12" y2="21"></line></svg>';
    }

    // Format instructions with line breaks
    const formattedInstructions = instructions.replace(/\n/g, '<br>');

    // Set modal content
    modalContent.innerHTML = `
        <div style="display: flex; align-items: center; margin-bottom: 15px;">
            <div style="margin-right: 15px; color: #0384c6;">${platformIcon}</div>
            <h2 style="margin: 0; font-size: 20px;">${title}</h2>
        </div>
        <div style="margin-bottom: 20px; line-height: 1.5;">${formattedInstructions}</div>
        <div style="text-align: right;">
            <button id="q-pwa-instructions-close" style="background-color: #0384c6; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">
                Got it
            </button>
        </div>
    `;

    // Add modal to document
    modal.appendChild(modalContent);
    document.body.appendChild(modal);

    // Add event listener to close button
    document.getElementById('q-pwa-instructions-close').addEventListener('click', () => {
        modal.remove();
    });

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

/**
 * Install the PWA
 */
async function installApp() {
    // For iOS, show instructions since the prompt isn't available
    if (detectedPlatform === 'ios' && qPwaData.showInstructions) {
        showPlatformInstructions();
        return;
    }

    if (!deferredPrompt) {
        console.error('Installation prompt not available');

        // If no prompt is available, show platform instructions
        if (qPwaData.showInstructions) {
            showPlatformInstructions();
        }
        return;
    }

    // Show the installation prompt
    deferredPrompt.prompt();

    // Wait for the user to respond to the prompt
    const choiceResult = await deferredPrompt.userChoice;

    // Log the result
    if (choiceResult.outcome === 'accepted') {
        console.log('User accepted the install prompt');

        // Remove the install button and floating button
        const container = document.getElementById('q-pwa-install-container');
        if (container) {
            container.remove();
        }

        const floatingButton = document.getElementById('q-pwa-floating-button');
        if (floatingButton) {
            floatingButton.remove();
        }
    } else {
        console.log('User dismissed the install prompt');
    }

    // Clear the deferred prompt
    deferredPrompt = null;
}

// Listen for app installed event
window.addEventListener('appinstalled', (event) => {
    console.log('PWA was installed');

    // Remove the install button if it exists
    const container = document.getElementById('q-pwa-install-container');
    if (container) {
        container.remove();
    }

    // Clear the deferred prompt
    deferredPrompt = null;
});
