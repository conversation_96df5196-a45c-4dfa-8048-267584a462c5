// Q-<PERSON>usher PWA Service Worker
const CACHE_NAME = '{{CACHE_NAME}}-{{CACHE_VERSION}}';
const OFFLINE_PAGE = '{{OFFLINE_PAGE}}';
const CACHE_URLS = {{CACHE_URLS}};

// Install event - cache assets
self.addEventListener('install', event => {
    console.log('[Q-PWA] Service Worker installing');

    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('[Q-PWA] Caching pages');

                // Ensure CACHE_URLS is an array
                let urlsToCache = CACHE_URLS;
                if (typeof CACHE_URLS === 'string') {
                    try {
                        urlsToCache = JSON.parse(CACHE_URLS);
                    } catch (e) {
                        console.error('[Q-PWA] Failed to parse CACHE_URLS:', e);
                        urlsToCache = ['/'];
                    }
                }

                if (!Array.isArray(urlsToCache)) {
                    console.error('[Q-PWA] CACHE_URLS is not an array, using default');
                    urlsToCache = ['/'];
                }

                // Make sure offline page is in the cache list
                const offlinePageUrl = new URL(OFFLINE_PAGE, self.location.origin).pathname;
                if (!urlsToCache.includes(offlinePageUrl)) {
                    urlsToCache.push(offlinePageUrl);
                }

                // Also add fallback offline paths for compatibility
                if (!urlsToCache.includes('/offline.html')) {
                    urlsToCache.push('/offline.html');
                }
                if (!urlsToCache.includes('/offline/index.html')) {
                    urlsToCache.push('/offline/index.html');
                }

                console.log('[Q-PWA] URLs to cache:', urlsToCache);

                // Cache each URL individually to avoid issues with addAll
                const cachePromises = urlsToCache.map(url => {
                    // Skip the /offline/ URL if it's in the list (we'll use offline.html instead)
                    if (url === '/offline/') {
                        console.log(`[Q-PWA] Skipping /offline/ URL, using /offline.html instead`);
                        return Promise.resolve();
                    }

                    // Create a proper request object with credentials
                    const request = new Request(url, { credentials: 'same-origin' });

                    return fetch(request)
                        .then(response => {
                            if (!response.ok) {
                                // For offline.html and offline/index.html, create a fallback if they don't exist
                                if (url === '/offline.html' || url === '/offline/index.html') {
                                    console.log(`[Q-PWA] Creating fallback for ${url}`);
                                    const fallbackResponse = new Response(
                                        '<html><head><title>Offline</title></head><body><h1>You are offline</h1><p>Please check your internet connection.</p></body></html>',
                                        {
                                            status: 200,
                                            headers: { 'Content-Type': 'text/html' }
                                        }
                                    );
                                    return cache.put(request, fallbackResponse);
                                }
                                throw new Error(`Request for ${url} returned status ${response.status}`);
                            }
                            return cache.put(request, response);
                        })
                        .catch(error => {
                            console.error(`[Q-PWA] Failed to cache ${url}:`, error);
                        });
                });

                return Promise.all(cachePromises);
            })
            .then(() => {
                console.log('[Q-PWA] Installed successfully');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('[Q-PWA] Cache install error:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('[Q-PWA] Service Worker activating');

    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames
                        .filter(cacheName => {
                            return cacheName.startsWith('{{CACHE_NAME}}-') && cacheName !== CACHE_NAME;
                        })
                        .map(cacheName => {
                            console.log('[Q-PWA] Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        })
                );
            })
            .then(() => {
                console.log('[Q-PWA] Service Worker activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', event => {
    // Skip cross-origin requests
    if (!event.request.url.startsWith(self.location.origin)) {
        return;
    }

    // Skip non-GET requests
    if (event.request.method !== 'GET') {
        return;
    }

    // Skip browser-extension requests
    if (event.request.url.includes('/wp-admin/') ||
        event.request.url.includes('/wp-login.php') ||
        event.request.url.includes('preview=true')) {
        return;
    }

    // Skip Firebase messaging requests (handled by Firebase service worker)
    if (event.request.url.includes('firebase-messaging-sw.js')) {
        return;
    }

    // Network first, falling back to cache, then offline page
    event.respondWith(
        fetch(event.request)
            .then(response => {
                // If we got a valid response, clone it and update the cache
                if (response && response.status === 200) {
                    const responseClone = response.clone();
                    caches.open(CACHE_NAME)
                        .then(cache => {
                            cache.put(event.request, responseClone);
                        });
                }
                return response;
            })
            .catch(() => {
                // If network fails, try to serve from cache
                return caches.match(event.request)
                    .then(cachedResponse => {
                        if (cachedResponse) {
                            return cachedResponse;
                        }

                        // If the request is for a page (HTML), serve the offline page
                        if (event.request.headers.get('Accept').includes('text/html')) {
                            // Try the configured offline page first
                            return caches.match(OFFLINE_PAGE)
                                .then(response => {
                                    if (response) {
                                        return response;
                                    }
                                    // Try fallback to the /offline.html
                                    return caches.match('/offline.html')
                                        .then(offlineHtmlResponse => {
                                            if (offlineHtmlResponse) {
                                                return offlineHtmlResponse;
                                            }
                                            // Try fallback to the /offline/index.html
                                            return caches.match('/offline/index.html')
                                                .then(offlineIndexResponse => {
                                                    if (offlineIndexResponse) {
                                                        return offlineIndexResponse;
                                                    }
                                                    // If all else fails, create a basic offline response
                                                    return new Response(
                                                        '<html><head><title>Offline</title></head><body><h1>You are offline</h1><p>Please check your internet connection.</p></body></html>',
                                                        {
                                                            status: 200,
                                                            headers: { 'Content-Type': 'text/html' }
                                                        }
                                                    );
                                                });
                                        });
                                });
                        }

                        // If we can't serve from cache and it's not a page, just fail
                        return new Response('Network error', {
                            status: 408,
                            headers: { 'Content-Type': 'text/plain' }
                        });
                    });
            })
    );
});

// Handle messages from the main thread
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});

// Import Firebase service worker functionality
// This section ensures compatibility with the Firebase messaging service worker
importScripts('https://www.gstatic.com/firebasejs/11.5.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/11.5.0/firebase-messaging-compat.js');

// Initialize Firebase with configuration details and error handling
try {
    firebase.initializeApp({
        apiKey: "{{FIREBASE_API_KEY}}",
        authDomain: "{{FIREBASE_AUTH_DOMAIN}}",
        projectId: "{{FIREBASE_PROJECT_ID}}",
        storageBucket: "{{FIREBASE_STORAGE_BUCKET}}",
        messagingSenderId: "{{FIREBASE_MESSAGING_SENDER_ID}}",
        appId: "{{FIREBASE_APP_ID}}"
    });
    const messaging = firebase.messaging();

    // Enhanced notification tracking with better validation
    const notificationCache = {
        messages: new Map(),
        cleanupInterval: 60000, // 60 seconds
        maxAge: 60000, // 60 seconds

        addMessage(key, timestamp) {
            if (!key) return false;
            this.cleanup();
            this.messages.set(key, timestamp);
            return true;
        },

        hasMessage(key) {
            if (!key) return false;
            this.cleanup();
            return this.messages.has(key);
        },

        cleanup() {
            const now = Date.now();
            let cleaned = 0;
            for (const [key, timestamp] of this.messages.entries()) {
                if (now - timestamp > this.maxAge) {
                    this.messages.delete(key);
                    cleaned++;
                }
            }
            if (cleaned > 0) {
                console.log(`Cleaned ${cleaned} old notifications`);
            }
        }
    };

    // Handle background messages with improved error handling and prevent duplicate notifications
    messaging.onBackgroundMessage(async (payload) => {
        try {
            console.log('Processing background message:', payload);

            // Handle data-only messages
            const { data } = payload;
            if (!data?.title) {
                console.error('Invalid notification payload');
                return;
            }

            const timestamp = parseInt(data.timestamp || Date.now() / 1000) * 1000;
            const now = Date.now();
            const notificationKey = data.message_id || `${data.title}-${timestamp}`;

            if (notificationCache.hasMessage(notificationKey)) {
                console.log('Duplicate notification filtered:', notificationKey);
                return;
            }

            if (now - timestamp > notificationCache.maxAge) {
                console.log('Discarding outdated notification:', notificationKey);
                return;
            }

            notificationCache.addMessage(notificationKey, now);

            // Create and show the notification
            const notificationOptions = {
                title: data.title,
                body: data.body,
                icon: data.icon || '/favicon.ico',
                data: {
                    url: data.click_action || self.registration.scope,
                    notification_id: data.notification_id || '',
                    form_id: data.form_id || ''
                },
                requireInteraction: true,
                tag: data.message_id || `notification-${Date.now()}`,
                click_action: data.click_action || self.registration.scope,
                badge: data.badge || data.icon || '/favicon.ico'
            };

            if (data.image) {
                notificationOptions.image = data.image;
            }

            return self.registration.showNotification(data.title, notificationOptions);
        } catch (error) {
            console.error('Error showing notification:', error);
        }
    });

    // Notification click handler with improved navigation
    self.addEventListener('notificationclick', event => {
        try {
            event.notification.close();

            // Get URL from notification data or fallback to scope
            let navigateUrl = event.notification.data?.url || self.registration.scope;

            // Ensure URL is absolute
            if (!navigateUrl.startsWith('http')) {
                navigateUrl = self.registration.scope + navigateUrl.replace(/^\//, '');
            }

            // Notify client to clear badge count
            self.clients.matchAll().then(clients => {
                clients.forEach(client => {
                    client.postMessage({
                        type: 'NOTIFICATION_CLICKED',
                        notification: {
                            id: event.notification.data?.notification_id || '',
                            title: event.notification.title
                        }
                    });
                });
            });

            // Track notification click with form_id
            const notificationData = event.notification.data || {};
            if (notificationData.notification_id) {
                fetch('/wp-admin/admin-ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'q_track_notification_click',
                        notification_id: notificationData.notification_id,
                        form_id: notificationData.form_id || ''
                    })
                }).catch(console.error);
            }

            // Focus existing window or open new one
            event.waitUntil(
                clients.matchAll({
                    type: 'window',
                    includeUncontrolled: true
                })
                .then(clientList => {
                    // Try to find an existing tab with the same origin
                    const matchingClient = clientList.find(client => {
                        const clientUrl = new URL(client.url);
                        const targetUrl = new URL(navigateUrl);
                        return clientUrl.origin === targetUrl.origin;
                    });

                    if (matchingClient) {
                        // If we found a matching tab, focus it and navigate
                        return matchingClient.focus().then(client => {
                            // Only navigate if URLs are different
                            if (client.url !== navigateUrl) {
                                return client.navigate(navigateUrl);
                            }
                        });
                    }

                    // If no existing tab found, check if we have any tab from our origin
                    const anyClientFromOrigin = clientList.find(client => {
                        const clientUrl = new URL(client.url);
                        const targetUrl = new URL(navigateUrl);
                        return clientUrl.origin === targetUrl.origin;
                    });

                    if (anyClientFromOrigin) {
                        // Reuse existing tab from same origin
                        return anyClientFromOrigin.focus().then(client => {
                            return client.navigate(navigateUrl);
                        });
                    }

                    // If no existing tabs found, open a new one
                    return clients.openWindow(navigateUrl);
                })
            );
        } catch (error) {
            console.error('Error handling notification click:', error);
            // Fallback to simple window open
            event.waitUntil(clients.openWindow(self.registration.scope));
        }
    });

    // Automated cleanup
    setInterval(() => notificationCache.cleanup(), notificationCache.cleanupInterval);

} catch (error) {
    console.error('Firebase initialization failed:', error);
}
